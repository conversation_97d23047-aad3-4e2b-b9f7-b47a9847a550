/* pages/foldshare/messageDetail/messageDetail.wxss */
.navsetting{
  --nav-bar-background-color: rgb(245, 245, 245);
  z-index: 100;  /* 降低导航栏层级 */
  position: relative;  /* 确保z-index生效 */
}

.gradient-background3 {
  min-height: 100vh;
  height: auto;
  background-color: rgb(245, 245, 245);
  padding-bottom: 130rpx;
}
.navtext{
  margin-bottom: 10rpx;
  font-family: '北航-刀隶体 Regular';
  font-size: 62rpx;
  margin-right: 287rpx;
}


/* 文字框样式 */
.num-item2 {
  margin: 16rpx 40rpx 40rpx 40rpx;  /* 减小顶部外边距 */
  box-sizing: border-box;
  border-radius: 34rpx;
  line-height: 60rpx;
  padding: 25rpx 25rpx 10rpx 25rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90%;
  word-wrap: break-word;
  background-color: #ffffff;
  transition: all 0.3s ease;
  position: relative; /* 添加相对定位 */
  isolation: isolate; /* 创建新的层叠上下文，防止样式影响到外部 */
}

/* 移除评论点击时的灰色背景效果 */
.num-item2 > view:active {
  background-color: transparent;
}

/* 评论长按时的效果 */
.num-item2:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.num-item2 .touxiang1 {
  background-color: transparent !important; /* 确保头像区域不会变色 */
}

.num-item2.highlighted {
  animation: highlight-comment 3s ease-out;
  box-shadow: 0 0 15rpx rgba(255, 182, 0, 0.7);
}

.reply-item {
  position: relative;
  margin: 10rpx 0;
  isolation: isolate;
}

.reply-content {
  position: relative;
  z-index: 1;
  padding: 10rpx;
  border-radius: 8rpx;
}

.reply-content view {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
}

.reply-content text {
  display: inline;
}

.reply-content text::before {
  content: attr(data-prefix);
  color: #808080;
}

/* 回复文字样式 */
.reply-content view {
  display: block;
  padding-left: 0;
  text-indent: 0;
}

.reply-click-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 50rpx;
  z-index: 2;
}

/* 确保点赞区域在点击区域之上 */
.last {
  position: relative;
  z-index: 3;
  padding: 0 10rpx;
  font-size: 25rpx;
  display: flex;
  align-items: center;
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
}

.last:active {
  background-color: transparent !important;
}

/* 调整点赞数字的对齐方式 */
.last text {
  display: inline-block;
  min-width: 20rpx;
  text-align: left;
  line-height: 35rpx;
}

/* 移除回复点击效果，只保留长按效果 */
.reply-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.reply-item .touxiang1 {
  background-color: transparent !important;
}

/* 确保楼中楼回复内容的间距与评论一致 */
.reply-item view > view:nth-child(2) {
  margin-top: 15rpx !important;
}

.reply-item.highlight-item {
  animation: highlight-reply 3s ease-out;
  box-shadow: 0 0 15rpx rgba(255, 182, 0, 0.7);
}

@keyframes highlight-comment {
  0% {
    background-color: rgba(255, 222, 173, 0.8);
    transform: translateX(-5rpx);
  }
  10% {
    background-color: rgba(255, 222, 173, 0.8);
    transform: translateX(5rpx);
  }
  20% {
    background-color: rgba(255, 222, 173, 0.8);
    transform: translateX(-5rpx);
  }
  30% {
    background-color: rgba(255, 222, 173, 0.8);
    transform: translateX(0);
  }
  100% {
    background-color: #ffffff;
    transform: translateX(0);
  }
}

@keyframes highlight-reply {
  0% {
    background-color: rgba(255, 222, 173, 0.8);
    transform: translateX(-5rpx);
  }
  10% {
    background-color: rgba(255, 222, 173, 0.8);
    transform: translateX(5rpx);
  }
  20% {
    background-color: rgba(255, 222, 173, 0.8);
    transform: translateX(-5rpx);
  }
  30% {
    background-color: rgba(255, 222, 173, 0.8);
    transform: translateX(0);
  }
  100% {
    background-color: #ffffff;
    transform: translateX(0);
  }
}

.touxiang1{
  margin-bottom: 10rpx;
  height: 60rpx;
  display: flex;
  background-color:transparent;
  border: 3rpx ;
  align-items: center;
}

.user-title {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #ff6b81;
  background-color: #fff0f3;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  border: 1rpx solid #ffccd5;
}

.gradient-text {
  background: linear-gradient(to right, #e434a9, #cc4444);
  -webkit-background-clip: text;
  color: transparent;
  font-size: 28rpx;
  line-height: 35rpx;
}
.bottom-fixed-bar {
  position: fixed;
  bottom: 50rpx;
  left: 24rpx;
  right: 24rpx;
  width: auto;
  background-color: #fff;
  padding: 16rpx 24rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  box-sizing: border-box;
  border-radius: 40rpx;
}



.input-section {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 15rpx 30rpx;
  margin-right: 30rpx;
}



.comment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.input-placeholder {
  color: #999;
  font-size: 28rpx;
}



.action-section {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  margin: -15rpx;
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
}

.action-item:active {
  background-color: transparent !important;
}

.action-icon {
  width: 35rpx;  /* 改回与评论区一致的尺寸 */
  height: 35rpx;  /* 改回与评论区一致的尺寸 */

}

.action-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}



.contact-icon {
  width: 90rpx;
  height: 45rpx;
}

.comment-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 998;  /* 降低遮罩层级 */
}

.fixed-comment-section {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  padding: 12rpx 24rpx;
  z-index: 999;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  transform: translateY(0);
  transition: transform 0.3s ease-out;
}

.comment-section-with-keyboard {
  transform: translateY(calc(-1 * var(--keyboard-height)));
}

.comment-section-with-emoji {
  transform: translateY(-400rpx);
}

.tools-row {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
  height: 72rpx;
  background-color: #fff;
  border-radius: 0 0 24rpx 24rpx;
}

.left-tools {
  display: flex;
  gap: 20rpx;
  align-items: center;
  padding: 0 10rpx;
}

.tool-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50rpx;
  height: 50rpx;
  padding: 10rpx;
  border-radius: 50%;
}

.tool-item:active {
  background-color: #f5f5f5;
}

.tool-icon {
  width: 50rpx;
  height: 50rpx;
}

.emoji-icon {
  font-size: 40rpx;
  line-height: 1;
}

.comment-textarea {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 18rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 1.4;
  margin: 8rpx 0;
  min-height: 80rpx;
  max-height: 200rpx;
  overflow-y: auto;
  transition: all 0.3s ease;
}

.quick-emoji-bar {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  padding: 16rpx 12rpx;
  background-color: transparent;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  margin: 0;
  gap: 8rpx;
}

.emoji-text {
  font-size: 40rpx;
  line-height: 1;
  text-align: center;
  transition: transform 0.2s ease;
}

.emoji-text:active {
  transform: scale(0.9);
}

/* 表情栏关闭时自动显示键盘 */
.fixed-comment-section:not(.comment-section-with-emoji) .comment-textarea {
  margin-bottom: 0;
}

.emoji-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 400rpx;
  background-color: #fff;
  z-index: 998;
  padding: 20rpx;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.emoji-scroll {
  height: 100%;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 5rpx;
  padding: 10rpx;
}

.emoji-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10rpx;
}

.emoji-image {
  width: 60rpx;
  height: 60rpx;
}

.submit-button {
  min-width: 120rpx;
  height: 56rpx;
  background-color: #07c160;
  color: #fff;
  border-radius: 28rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  margin: 0;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

/* 禁用状态样式 */
.submit-button[disabled] {
  background-color: #ddd;
  color: #888;
  box-shadow: none;
  cursor: not-allowed;
}

/* 发送中的加载动画 */
.submit-button[disabled]::before {
  content: "";
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 3rpx solid #ccc;
  border-top-color: #888;
  animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }
  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

.image-container {
  margin-top: 15rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 9rpx;
  margin-bottom: 16rpx;
  z-index: 5;
  position: relative;
  -webkit-tap-highlight-color: transparent !important;
}

/* 添加以下样式确保回复中的图片容器也是三列一排布局 */
.reply-item .image-container {
  margin-top: 15rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 9rpx;
  margin-bottom: 16rpx;
  z-index: 5;
  position: relative;
  -webkit-tap-highlight-color: transparent !important;
}

.uniform-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
  z-index: 10;
  position: relative;
}
.divider {
  height: 4rpx;
  background-color: #e5e5e5;
  margin: 0 40rpx;
}



/* 赛博朋克风格帖主头衔 - 红黄配色静态版 */
.cyber-badge {
  position: relative;
  height: 28rpx;
  width: 64rpx;
  margin-left: 10rpx;
  display: inline-block;
}

.cyber-btn {
  --primary: #ff184c;
  --shadow-primary: #fded00;
  --color: white;
  --clip: polygon(11% 0, 95% 0, 100% 25%, 90% 90%, 95% 90%, 85% 90%, 85% 100%, 7% 100%, 0 80%);
  --border: 1.5rpx;

  color: var(--color);
  text-transform: uppercase;
  font-size: 18rpx;
  letter-spacing: 0.3rpx;
  position: relative;
  font-weight: 900;
  width: 100%;
  height: 100%;
  line-height: 28rpx;
  text-align: center;
}

.cyber-btn::after, .cyber-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  clip-path: var(--clip);
  z-index: -1;
}

.cyber-btn::before {
  background: var(--shadow-primary);
  transform: translate(var(--border), 0);
}

.cyber-btn::after {
  background: var(--primary);
}

.cyber-number {
  background: var(--shadow-primary);
  color: #323232;
  font-size: 10rpx;
  font-weight: 700;
  letter-spacing: 0.1rpx;
  position: absolute;
  width: 20rpx;
  height: 8rpx;
  top: 0;
  left: 78%;
  line-height: 8rpx;
  text-align: center;
}

/* 保留原有的普通头衔样式 */
.touxian2{
  padding: 7rpx;
  margin-left:15rpx;
  /* background-image: linear-gradient(to left, #f64f59, #c471ed, #12c2e9); */
  color: white;
  font-size: 20rpx;
  border-radius: 8rpx;
  line-height: 1
}
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 998;
  backdrop-filter: blur(4px);
}

.modal {
  padding-bottom: 30rpx;
  position: fixed;
  left: 50%;
  top: 50%;
  background-color: #ffffff;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 999;
  transform: translate(-50%, -50%);
  animation: modalShow 0.3s ease forwards;
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: translate(-50%, -45%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.modal-header3 {
  margin-top: 0;
  text-align: center;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #f6d365 0%, #fda085 100%);
  padding: 30rpx 20rpx;
  position: relative;
  overflow: hidden;
}

.modal-header3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40rpx;
  background: #ffffff;
  border-radius: 50% 50% 0 0;
}

.modal-header4 {
  position: relative;
  z-index: 1;
  text-align: center;
  font-size: 36rpx;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
}

.profile-container2 {
  height: auto;
  margin: 20rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  animation: slideUp 0.3s ease forwards;
  animation-delay: calc(var(--index) * 0.1s);
  opacity: 0;
  transform: translateY(20rpx);
}

.profile-container2:active {
  background-color: #f0f1f2;
  transform: translateY(-2rpx);
}

.label {
  flex-shrink: 0;
  font-size: 28rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.label::before {
  content: '';
  display: inline-block;
  width: 8rpx;
  height: 8rpx;
  background-color: #fda085;
  border-radius: 50%;
  margin-right: 12rpx;
}

.input-right {
  font-size: 28rpx;
  text-align: right;
  color: #333;
  font-weight: 500;
  min-height: 40rpx;
  min-width: 60rpx;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 1001;
  width: 64rpx;
  height: 64rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

.modal-close:active {
  transform: scale(0.9);
  background-color: rgba(255, 255, 255, 0.3);
}

.text{
  font-size: 32rpx;
  white-space: pre-wrap;
  word-break: break-word;
  margin-bottom: 5rpx;  /* 从10rpx减小到5rpx，缩短评论与时间的距离 */
}

.bottom-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 110rpx;
  background-color: rgb(245, 245, 245);
  z-index: 99;  /* 降低底部遮罩层级 */
}



/* 图片预览区域 */
.preview-area {
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 8rpx;
}

.preview-images {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.preview-image-item {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.close-btn {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  z-index: 1;
}

.preview-text {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 4rpx 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  text-align: center;
}

.image-count {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 6rpx;
  background-color: #ff4444;
  color: #fff;
  border-radius: 16rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 评论功能栏样式 */
.comment-function-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx 12rpx 40rpx;
  margin: 0;
  background-color: #f7f7f7;
  position: relative;  /* 添加定位 */
  z-index: auto !important;  /* 移除父元素的z-index限制 */
}



.left-section {
  font-size: 28rpx;  /* 比排序按钮大2rpx */
  color: #333;
  font-weight: 500;
  padding: 6rpx 20rpx;
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  width: auto;
  text-align: center;
  height: 58rpx;  /* 与右侧保持一致 */
  line-height: 48rpx;  /* 文字垂直居中 */
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 翻转文字样式 */
.flip-text {
  position: relative;
  width: 95rpx;  /* 从84rpx增加到89rpx */
  height: 100%;
  cursor: pointer;
}

.flip-text.flipped .flip-inner {
  transform: rotateY(180deg);
}

.flip-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.flip-front, .flip-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flip-front {
  color: #333;
}

.flip-back {
  background: linear-gradient(45deg, #ff6b6b, #feca57);
  -webkit-background-clip: text;
  color: transparent;
  transform: rotateY(180deg);
}

.right-section {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 32rpx;
  padding: 4rpx;
  position: relative;
  width: 200rpx;
  height: 48rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: auto !important;  /* 移除父元素的z-index限制 */
}

.sort-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50%;
  height: 100%;
  font-size: 26rpx;
  color: #999;
  background: transparent;
  border: none;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  margin: 0;
  padding: 0;
  border-radius: 32rpx;
  gap: 0;  /* 移除间距 */
}

.sort-btn text {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-right: 0rpx;  /* 从5rpx调整到20rpx */
}

.sort-btn text:first-child {
  margin-right: -2rpx;  /* 让文字和箭头更靠近 */
}

.sort-btn::after {
  display: none;
}

.sort-btn.active {
  color: #333;
  font-weight: 500;
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  padding-left: 7rpx;
}

.sort-arrow {
  font-size: 24rpx;
  color: #999;
  font-weight: bold;
  transition: transform 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20rpx;  /* 减小箭头宽度 */
  text-align: center;
  margin-left: 0;  /* 移除左边距 */
}

.arrow-up {
  transform: rotate(-180deg);
  color: #ff4444 !important;
  font-weight: 900;
}

.arrow-down {
  transform: rotate(0deg);
  color: #1890ff !important;
  font-weight: 900;
}

/* 无评论样式 */
.no-comment {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  margin: 20rpx 40rpx;
  margin-top: 100rpx;  /* 增加顶部间距 */
}

.no-comment-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.no-comment-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}



/* 加载中样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  margin-top: 60rpx;
}

/* 移除加载动画相关样式 */
.loading-dots {
  display: none;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.contact-btn {
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx 4rpx 16rpx;  /* 左侧内边距从12rpx增加到16rpx */
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 28rpx;
  gap: 4rpx;
}

.contact-btn:active {
  background-color: rgba(0, 0, 0, 0.1);
}


.contact-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1;
  margin-left: 4rpx;  /* 添加左侧文字的额外间距 */
}

/* 移除不需要的样式 */
.middle-section,
.divider-vertical {
  display: none;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 删除之前的翻转容器样式 */

/* emoji表情面板样式 */
.emoji-list-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 400rpx;
  background-color: #fff;
  z-index: 998;
  padding: 20rpx;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.emoji-list-scroll {
  height: 100%;
  width: 100%;
}

.emoji-list-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 10rpx;
  padding: 20rpx;
}

.emoji-list-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15rpx;
  font-size: 40rpx;
  transition: transform 0.2s ease;
}

.emoji-list-item:active {
  transform: scale(0.9);
}

.emoji-icon {
  font-size: 40rpx;
  line-height: 1;
}

/* 修改评论区域样式，适配emoji面板 */
.comment-section-with-emoji-list {
  transform: translateY(-400rpx);
}

.like-wrapper {
  padding: 20rpx;  /* 从15rpx增加到20rpx，扩大点击区域 */
  margin: -20rpx;  /* 从-15rpx改为-20rpx，抵消padding造成的位移 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.like-wrapper image {
  display: block;  /* 确保图片正确显示 */
}

/* 引导提示相关样式 */
.guide-hint-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 8000;
  pointer-events: auto;
}

.mask-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
}

.guide-show {
  opacity: 1;
  transition: all 0.3s ease-in-out;
}

.guide-hide {
  opacity: 0;
  transition: all 0.3s ease-in-out;
  pointer-events: none;
}

.guide-closing {
  pointer-events: none;
}

.guide-content {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 8001;
}

.guide-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  pointer-events: auto;
  z-index: 9999;
}

.guide-tip-text {
  background-color: white;
  padding: 30rpx 40rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 600rpx;
  line-height: 1.5;
  white-space: normal;
}

.guide-tip-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.guide-btn {
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 30rpx;
  background: #ff6b6b;
  color: white;
  border: none;
  line-height: 1.5;
  box-shadow: 0 4rpx 8rpx rgba(255, 107, 107, 0.2);
}

.guide-btn.guide-skip {
  background: #999;
}

.guide-btn::after {
  border: none;
}

.guide-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 高亮效果 */
.guide-active {
  position: relative !important;
  z-index: 9000 !important;
  box-shadow: 0 0 20rpx rgba(255, 255, 255, 0.8) !important;
}

/* 排序按钮高亮 */
.right-section.guide-active {
  background: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 0 20rpx rgba(255, 107, 107, 0.3) !important;
  border-radius: 32rpx !important;
  z-index: 9000 !important;
}

/* 移除旧的样式 */
.guide-hint-content,
.guide-hint-body,
.guide-hint-footer,
.guide-highlight-area {
  display: none;
}

/* 操作菜单样式 */
.action-sheet-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  font-size: 32rpx;
  color: #333;
  background: #fff;
  position: relative;
}

.action-sheet-item:active {
  background: #f5f5f5;
}

.action-sheet-item.delete {
  color: #FF4D4F;
}

.action-sheet-item + .action-sheet-item::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: 1px;
  background: #eee;
  transform: scaleY(0.5);
}

/* 确认删除弹窗样式 */
.confirm-modal {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
}

.confirm-modal .title {
  font-size: 36rpx;
  font-weight: 500;
  text-align: center;
  padding: 40rpx 0 20rpx;
}

.confirm-modal .content {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  padding: 0 40rpx 40rpx;
}

.confirm-modal .buttons {
  display: flex;
  border-top: 1px solid #eee;
}

.confirm-modal .button {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
}

.confirm-modal .button.cancel {
  color: #666;
  background: #f5f5f5;
}

.confirm-modal .button.confirm {
  color: #fff;
  background: linear-gradient(135deg, #FF4D4F 0%, #F5222D 100%);
}

/* 高亮选中的评论/回复 */
.highlighted {
  background: rgba(255, 77, 79, 0.05);
}

/* 投票区域样式 */
.vote-container {
  margin: 0 0 30rpx;
  padding: 0;
  background-color: transparent;
  position: relative;
  -webkit-tap-highlight-color: transparent;  /* 移除点击时的灰色背景 */
}

.vote-container:active {
  background-color: transparent !important;
}

.vote-header {
  margin-bottom: 16rpx;
}

.vote-title-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.vote-title-icon {
  width: 28rpx;
  height: 28rpx;
}

.vote-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.vote-type-tag {
  font-size: 22rpx;
  color: #fff;
  background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
  padding: 4rpx 12rpx;
  border-radius: 24rpx;
  display: inline-flex;
  align-items: center;
  height: fit-content;
  line-height: 1.4;
  box-shadow: 0 2rpx 6rpx rgba(255, 165, 0, 0.2);
}

.vote-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.vote-option-box {
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 28rpx;
  padding: 16rpx 20rpx;
  position: relative;
  overflow: hidden;
  background-color: transparent;
  min-height: 60rpx;
  box-sizing: border-box;
  transition: all 0.2s ease;
  -webkit-tap-highlight-color: transparent;  /* 移除点击时的灰色背景 */
}

/* 添加点击反馈效果 */
.vote-option-box:active {
  transform: scale(0.98);
  border-color: rgba(255, 165, 0, 0.5);
  background-color: rgba(255, 165, 0, 0.05);
}

.vote-option-box.selected {
  border-color: rgba(255, 165, 0, 0.8);
  background-color: rgba(255, 165, 0, 0.05);
}

.vote-progress-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 28rpx;
  overflow: hidden;
  z-index: 0;
}

.vote-progress-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: rgba(255, 165, 0, 0.15);
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);  /* 使用更平滑的动画曲线 */
}

.vote-option-box.selected .vote-progress-fill {
  background: rgba(255, 165, 0, 0.35);
}

.vote-option-box.voted {
  border-color: rgba(255, 165, 0, 0.8);
}

.vote-option-box.voted .vote-progress-fill {
  background: rgba(255, 165, 0, 0.35);
}

.vote-option-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 36rpx;
}

.vote-option-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 12rpx;
  line-height: 1.5;
  flex: 1;
}

.vote-count-text {
  font-size: 22rpx;
  color: #666;
  flex-shrink: 0;
  min-width: 40rpx;
  text-align: right;
  line-height: 1.5;
}

.vote-button-area {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s ease;
  pointer-events: none;
  visibility: hidden;
}

.vote-button-area.show-multi {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
  visibility: visible;
}

.vote-button-area.show-single {
  display: none;
}

.vote-submit-button {
  background-color: #ddd;
  color: #888;
  font-size: 26rpx;
  padding: 12rpx 50rpx;
  border-radius: 28rpx;
  border: none;
  transition: all 0.3s ease;
  -webkit-tap-highlight-color: transparent;  /* 移除点击时的灰色背景 */
}

.vote-submit-button.active {
  background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(255, 165, 0, 0.3);
}

.vote-submit-button.active:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 165, 0, 0.2);
}

/* 添加选项选中和取消选中的动画 */
@keyframes optionSelected {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

.vote-option-box.animate-select {
  animation: optionSelected 0.3s ease;
}

/* 高亮样式 */
.highlight-item {
  animation: highlight-animation 3s ease-out;
}

@keyframes highlight-animation {
  0% {
    background-color: rgba(255, 251, 214, 1);
  }
  70% {
    background-color: rgba(255, 251, 214, 0.8);
  }
  100% {
    background-color: transparent;
  }
}

/* 回复展开按钮样式 */
.expand-replies-btn {
  position: relative;
  z-index: 3;
  background-color: transparent;
  padding: 5rpx 0;
  color: #576b95;
  font-size: 28rpx;
  margin-top: 5rpx;
}

.expand-text {
  display: inline-block;
  padding: 5rpx 20rpx;
  color: #576b95;
  font-size: 28rpx;
}

.expand-icon {
  margin-right: 8rpx;
  font-weight: bold;
}

/* 确保底部的时间和点赞区域不会变灰 */
.kuang {
  position: relative;
  z-index: 2;
  background-color: transparent;
}

.kuang:active {
  background-color: transparent !important;
}

.kuang {
  height: 60rpx;
  margin-top: auto;
  display: flex;
  align-items: center;
}

.kuang .last {
  font-size: 25rpx;
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 35rpx;
  line-height: 35rpx;
}

.kuang .action-item {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
}

.kuang .action-item image {
  width: 35rpx;
  height: 35rpx;
  margin-left: 10rpx;
}

.kuang .action-item view {
  margin-left: 10rpx;
  color: #000000;
  font-size: 25rpx;
}

.manage-btn {
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.manage-dots {
  font-size: 40rpx;
  font-weight: bold;
  color: #666;
  line-height: 1;
  display: inline-block;
}

.manage-btn:active {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 10rpx;
}

.share-button {
  position: fixed;
  opacity: 0;
  width: 0;
  height: 0;
  padding: 0;
  margin: 0;
  top: -100rpx;
  left: -100rpx;
}

/* 自定义菜单样式 */
.custom-menu-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.custom-menu {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1001;
  padding: 30rpx 0;
  animation: slideUp 0.3s ease;
}

.custom-menu-item {
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 40rpx;
  margin: 0;
  background-color: #fff;
  border: none;
  width: 100%;
  box-sizing: border-box;
  text-align: left;
  line-height: 100rpx;
  border-radius: 0;
}

.custom-menu-item::after {
  border: none;
}

.custom-menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.custom-menu-item text {
  font-size: 32rpx;
  color: #333;
}

.delete-text {
  color: #ff4d4f !important;
}

.custom-menu-item:active {
  background-color: #f5f5f5;
}

/* 分享面板样式 */
.share-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.share-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  z-index: 1000;
  animation: slideUp 0.3s ease-out;
}

.share-panel-title {
  font-size: 30rpx;
  color: #666;
  text-align: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f2f2f2;
}

.share-options {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  line-height: normal;
  padding: 0;
  margin: 0 40rpx;
  width: auto;
}

.share-option::after {
  border: none;
}

.share-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.share-option text {
  font-size: 28rpx;
  color: #333;
}

.share-cancel {
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  border-top: 10rpx solid #f2f2f2;
}

.share-cancel:active {
  background-color: #f5f5f5;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.action-sheet-share-button {
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  opacity: 0 !important;
  z-index: 9999 !important;
}

/* 直接分享按钮样式 */
.direct-share-button {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #07c160;
  color: #fff;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 9999;
  border: none;
}

.direct-share-button::after {
  border: none;
}

/* 移除旧的分享按钮样式 */
.share-button {
  display: none;
}

/* 自定义ActionSheet样式 */
.custom-action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.custom-action-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 12rpx 12rpx 0 0;
  overflow: hidden;
  z-index: 1000;
  animation: slideUp 0.2s ease-out;
}

.custom-action-sheet-title {
  font-size: 28rpx;
  color: #888;
  text-align: center;
  padding: 20rpx 0;
  display: none;
}

.custom-action-sheet-options {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.custom-action-option {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 110rpx;
  background-color: #fff;
  line-height: 110rpx;
  padding: 0;
  margin: 0;
  width: 100%;
  border-bottom: 1rpx solid #f2f2f2;
  position: relative;
}

button.custom-action-option {
  border: none;
  border-radius: 0;
}

button.custom-action-option::after {
  border: none;
}

.custom-action-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.custom-action-option text {
  font-size: 32rpx;
  color: #333;
}

.share-option text {
  color: #333;
}

.delete-option text {
  color: #e64340;
}

.custom-action-cancel {
  height: 110rpx;
  line-height: 110rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  background-color: #fff;
  border-top: 10rpx solid #f2f2f2;
}

.custom-action-cancel:active, .custom-action-option:active {
  background-color: #f7f7f7;
}

.comment-content {
  position: relative;
  z-index: 1;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
}

.comment-content:active,
.comment-content:hover {
  background-color: transparent !important;
  color: inherit !important;
}

.num-item2 view,
.num-item2 text,
.num-item2 image,
.reply-item view, 
.reply-item text,
.reply-item image {
  -webkit-tap-highlight-color: transparent !important;
}

view {
  -webkit-tap-highlight-color: transparent !important;
}

.comment-content,
.reply-content,
.kuang,
.num-item2,
.reply-item {
  -webkit-tap-highlight-color: transparent !important;
}

.comment-content:active,
.reply-content:active,
.kuang:active,
.num-item2:active,
.reply-item:active {
  background-color: transparent !important;
}

.comment-content > view:active,
.reply-content > view:active,
.kuang > view:active,
.num-item2 > view:active,
.reply-item > view:active {
  background-color: transparent !important;
}

/* 长按评论项的效果 */
.num-item2.active-longpress {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

/* 长按回复项的效果 */
.reply-item.active-longpress {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

page {
  -webkit-tap-highlight-color: transparent !important;
  user-select: none;
}

/* 各个元素的透明点击效果 */
.num-item2 view,
.num-item2 text,
.num-item2 image,
.reply-item view,
.reply-item text,
.reply-item image,
.comment-content,
.reply-content,
.kuang,
.num-item2,
.reply-item,
view {
  -webkit-tap-highlight-color: transparent !important;
}

/* 活跃状态背景透明 */
.comment-content:active,
.reply-content:active,
.kuang:active,
.num-item2:active,
.reply-item:active,
.comment-content > view:active,
.reply-content > view:active,
.kuang > view:active,
.num-item2 > view:active,
.reply-item > view:active {
  background-color: transparent !important;
}

/* 添加profile-container2的slideUp动画定义 */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 适配模态窗口中没有数据的样式 */
.profile-container2 .input-right {
  font-size: 28rpx;
  text-align: right;
  color: #333;
  font-weight: 500;
  min-height: 40rpx;
  min-width: 60rpx;
}

/* 确保点赞区域不会有任何背景色变化 */
.last image,
.action-item image {
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
}



.last image:active,
.action-item image:active {
  background-color: transparent !important;
}

/* 全局点击效果处理 */
page {
  -webkit-tap-highlight-color: transparent !important;
  user-select: none;
}

/* 移除所有元素的点击高亮效果 */
view, text, image, button {
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
}

/* 评论和回复项的基础样式 */
.num-item2, .reply-item {
  -webkit-tap-highlight-color: transparent !important;
  background-color: #ffffff;
  transition: background-color 0.2s ease;
}

/* 只在长按时显示背景色变化 */
.num-item2.active-longpress,
.reply-item.active-longpress {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

/* 点赞按钮区域样式 */
.last {
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
  position: relative;
  z-index: 2;
}

.last:active,
.last:hover,
.last image:active,
.last image:hover {
  background-color: transparent !important;
  opacity: 1 !important;
}

/* 底部固定栏样式 */
.bottom-fixed-bar {
  -webkit-tap-highlight-color: transparent !important;
}

.action-item {
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
  position: relative;
  z-index: 2;
}

.action-item:active,
.action-item:hover,
.action-item image:active,
.action-item image:hover {
  background-color: transparent !important;
  opacity: 1 !important;
}

/* 评论内容区域样式 */
.comment-content,
.reply-content {
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
}

.comment-content:active,
.reply-content:active {
  background-color: transparent !important;
}

/* 时间戳区域样式 */
.gradient-text {
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
}

.gradient-text:active {
  background-color: transparent !important;
}

.report-option {
  color: #FF4D4F;
}

.report-option text {
  color: #FF4D4F;
}

/* 联系方式弹窗 - 从下方弹出 */
.contact-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 999;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.contact-modal-show {
  opacity: 1;
  visibility: visible;
}

.contact-modal-content {
  width: 100%;
  background-color: rgb(240, 240, 240);
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  padding-bottom: env(safe-area-inset-bottom, 20rpx);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.contact-modal-show .contact-modal-content {
  transform: translateY(0);
}

.contact-modal-header {
  position: relative;
  text-align: center;
  padding: 30rpx 20rpx 20rpx;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
}

.contact-modal-title {
  font-size: 45rpx;
  color: rgb(0, 0, 0);
  font-weight: bold;
}

.contact-modal-close {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.651);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-btn {
  background-color: #007aff;
  color: white;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
  transition: all 0.2s ease;
}

.copy-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.contact-modal-footer {
  padding: 30rpx 50rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom, 20rpx));
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-tip {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
  text-align: center;
}

.contact-confirm-btn {
  background-color: white;
  color: #333;
  border: 2rpx solid #333;
  padding: 25rpx 80rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
  min-width: 200rpx;
}

.contact-confirm-btn:active {
  transform: scale(0.96);
  background-color: #f5f5f5;
}
